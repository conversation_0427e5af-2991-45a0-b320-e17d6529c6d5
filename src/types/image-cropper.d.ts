export interface CropData {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface CropperSettings {
  aspectRatio?: number;
  viewMode: number;
  dragMode: 'crop' | 'move' | 'none';
  autoCropArea: number;
  responsive: boolean;
  restore: boolean;
  guides: boolean;
  center: boolean;
  highlight: boolean;
  cropBoxMovable: boolean;
  cropBoxResizable: boolean;
  toggleDragModeOnDblclick: boolean;
}

export interface ImageCropperProps {
  className?: string;
  onCropChange?: (cropData: CropData) => void;
  onImageLoad?: (imageUrl: string) => void;
}

export interface CropperControlsState {
  width: number;
  height: number;
  x: number;
  y: number;
}
