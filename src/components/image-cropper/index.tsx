"use client";

import React, { useState, useRef, useCallback } from 'react';
import <PERSON><PERSON><PERSON>, { ReactCropperElement } from 'react-cropper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Upload, Download, RotateCcw, Settings, ChevronUp, ChevronDown, ZoomIn, ZoomOut, Maximize, RotateCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ImageCropperProps, CropperControlsState, CropData } from '@/types/image-cropper';

export default function ImageCropper({ className, onCropChange, onImageLoad }: ImageCropperProps) {
  const cropperRef = useRef<ReactCropperElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [image, setImage] = useState<string>('');
  const [controls, setControls] = useState<CropperControlsState>({
    width: 200,
    height: 200,
    x: 0,
    y: 0
  });
  const [isControlsExpanded, setIsControlsExpanded] = useState<boolean>(true);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [showZoomIndicator, setShowZoomIndicator] = useState<boolean>(false);

  // 处理文件上传
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        const imageUrl = reader.result as string;
        setImage(imageUrl);
        onImageLoad?.(imageUrl);
      };
      reader.readAsDataURL(file);
    }
  }, [onImageLoad]);

  // 处理裁剪数据变化
  const handleCropChange = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const cropData = cropper.getData();
      const newControls = {
        ...controls,
        width: Math.round(cropData.width),
        height: Math.round(cropData.height),
        x: Math.round(cropData.x),
        y: Math.round(cropData.y)
      };
      setControls(newControls);

      const cropInfo: CropData = {
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      };
      onCropChange?.(cropInfo);
    }
  }, [controls, onCropChange]);

  // 处理缩放事件
  const handleZoomEvent = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const imageData = cropper.getImageData();
      const currentZoom = imageData.naturalWidth > 0 ? imageData.width / imageData.naturalWidth : 1;
      setZoomLevel(currentZoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  // 处理控制面板数值变化
  const handleControlChange = useCallback((field: keyof CropperControlsState, value: number) => {
    const newControls = { ...controls, [field]: value };
    setControls(newControls);

    if (cropperRef.current?.cropper) {
      const cropper = cropperRef.current.cropper;
      cropper.setData({
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      });
    }
  }, [controls]);



  // 下载裁剪后的图片
  const handleDownload = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const canvas = cropper.getCroppedCanvas({
        width: controls.width,
        height: controls.height,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
      });

      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `cropped-image-${Date.now()}.png`;
          a.click();
          URL.revokeObjectURL(url);
        }
      });
    }
  }, [controls]);

  // 设置预设尺寸
  const handlePresetSize = useCallback((width: number, height: number) => {
    const newControls = { ...controls, width, height };
    setControls(newControls);
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.setData({
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      });
    }
  }, [controls]);

  // 缩放控制
  const handleZoom = useCallback((delta: number) => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.zoom(delta);
      const imageData = cropper.getImageData();
      const currentZoom = imageData.naturalWidth > 0 ? imageData.width / imageData.naturalWidth : 1;
      setZoomLevel(currentZoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  const handleZoomTo = useCallback((zoom: number) => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.zoomTo(zoom);
      setZoomLevel(zoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  const handleFitToContainer = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.reset();
      setZoomLevel(1);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  // 重置
  const handleReset = useCallback(() => {
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.reset();
    }
    setControls({
      width: 200,
      height: 200,
      x: 0,
      y: 0
    });
    setZoomLevel(1);
  }, []);

  return (
    <section className={cn("py-12", className)}>
      <div className="container">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">图片裁剪工具</h2>
          <p className="text-muted-foreground">上传图片并进行精确裁剪</p>
        </div>
        
        {/* 响应式布局：移动端上下堆叠，桌面端左右分栏 */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* 移动端：图片预览区在上方 */}
          <div className="lg:col-span-8 lg:order-2 order-1">
            <Card className="h-full">
              <CardContent className="p-6 h-full flex flex-col">
                {image ? (
                  <div className="w-full relative flex-1 min-h-[500px]">
                    <Cropper
                      ref={cropperRef}
                      src={image}
                      style={{ height: '100%', width: '100%' }}
                      initialAspectRatio={NaN}
                      guides={true}
                      crop={handleCropChange}
                      cropend={handleCropChange}
                      ready={handleCropChange}
                      zoom={handleZoomEvent}
                      viewMode={1}
                      dragMode="crop"
                      scalable={true}
                      cropBoxMovable={true}
                      cropBoxResizable={true}
                      toggleDragModeOnDblclick={false}
                      responsive={true}
                      restore={false}
                      center={true}
                      highlight={true}
                      autoCropArea={0.8}
                    />

                    {/* 缩放指示器 */}
                    {showZoomIndicator && (
                      <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-md text-sm font-medium transition-opacity duration-300">
                        {Math.round(zoomLevel * 100)}%
                      </div>
                    )}

                    {/* 缩放提示 */}
                    <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-md text-xs">
                      滚轮缩放
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center flex-1 min-h-[500px] border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <div className="text-center">
                      <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground">请先选择一张图片</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 控制面板 */}
          <div className="lg:col-span-4 lg:order-1 order-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle
                  className="flex items-center justify-between cursor-pointer lg:cursor-default"
                  onClick={() => setIsControlsExpanded(!isControlsExpanded)}
                >
                  <div className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    裁剪设置
                  </div>
                  <div className="lg:hidden">
                    {isControlsExpanded ? (
                      <ChevronUp className="w-5 h-5" />
                    ) : (
                      <ChevronDown className="w-5 h-5" />
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className={cn(
                "space-y-4 transition-all duration-300 flex-1 flex flex-col",
                !isControlsExpanded && "lg:flex hidden"
              )}>
                {/* 文件上传 */}
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <Button 
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="w-full"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    选择图片
                  </Button>
                </div>

                {/* 尺寸控制 */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-sm font-medium">宽度</label>
                    <Input
                      type="number"
                      value={controls.width}
                      onChange={(e) => handleControlChange('width', parseInt(e.target.value) || 0)}
                      min="1"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">高度</label>
                    <Input
                      type="number"
                      value={controls.height}
                      onChange={(e) => handleControlChange('height', parseInt(e.target.value) || 0)}
                      min="1"
                    />
                  </div>
                </div>

                {/* 位置控制 */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-sm font-medium">X坐标</label>
                    <Input
                      type="number"
                      value={controls.x}
                      onChange={(e) => handleControlChange('x', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Y坐标</label>
                    <Input
                      type="number"
                      value={controls.y}
                      onChange={(e) => handleControlChange('y', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                </div>

                {/* 缩放控制 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">缩放控制</label>
                  <div className="space-y-2">
                    {/* 缩放级别显示 */}
                    <div className="flex items-center justify-between text-sm">
                      <span>当前缩放:</span>
                      <span className="font-medium">{Math.round(zoomLevel * 100)}%</span>
                    </div>

                    {/* 缩放滑块 */}
                    <div className="flex items-center gap-2">
                      <ZoomOut className="w-4 h-4 text-muted-foreground" />
                      <input
                        type="range"
                        min="0.1"
                        max="5"
                        step="0.1"
                        value={zoomLevel}
                        onChange={(e) => handleZoomTo(parseFloat(e.target.value))}
                        disabled={!image}
                        className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
                      />
                      <ZoomIn className="w-4 h-4 text-muted-foreground" />
                    </div>

                    {/* 缩放按钮 */}
                    <div className="grid grid-cols-4 gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoom(-0.1)}
                        disabled={!image}
                        title="缩小"
                      >
                        <ZoomOut className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoom(0.1)}
                        disabled={!image}
                        title="放大"
                      >
                        <ZoomIn className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoomTo(1)}
                        disabled={!image}
                        title="100%"
                      >
                        1:1
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleFitToContainer}
                        disabled={!image}
                        title="适应窗口"
                      >
                        <Maximize className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>



                {/* 预设尺寸 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">常用尺寸</label>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(200, 200)}
                    >
                      200×200
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(400, 300)}
                    >
                      400×300
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(800, 600)}
                    >
                      800×600
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(1920, 1080)}
                    >
                      1920×1080
                    </Button>
                  </div>
                </div>

                {/* 填充空间 */}
                <div className="flex-1"></div>

                {/* 操作按钮 */}
                <div className="space-y-2 mt-auto">
                  <Button
                    onClick={handleDownload}
                    disabled={!image}
                    className="w-full"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载图片
                  </Button>
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    disabled={!image}
                    className="w-full"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    重置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>
      </div>
    </section>
  );
}
